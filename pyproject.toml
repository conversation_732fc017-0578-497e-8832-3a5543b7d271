[project]
name = "offset-v2"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aider>=0.2.6",
    "cst-lsp>=0.1.3",
    "cupy-cuda12x>=13.4.1",
    "curvey>=0.0.4",
    "cvxpy>=1.6.5",
    "fast-tsp>=0.1.2",
    "geopandas>=1.0.1",
    "google-generativeai>=0.8.4",
    "ipykernel>=6.29.5",
    "jax[cuda12]>=0.6.2",
    "jaxlib>=0.6.2",
    "matplot>=0.1.9",
    "networkx>=3.4.2",
    "numba>=0.61.2",
    "numpy>=2.2.3",
    "opencv-python>=*********",
    "ortools>=9.12.4544",
    "pillow>=11.1.0",
    "pyclothoids>=0.1.5",
    "pyvoronoi>=1.1.6",
    "rtree>=1.3.0",
    "scikit-image>=0.25.2",
    "scikit-learn>=1.7.0",
    "scipy>=1.15.2",
    "shapely>=2.0.7",
    "trimesh>=4.6.4",
]
